//
//  SettingsView.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/6/28.
//

import SwiftUI

struct SettingsView: View {
    @ObservedObject var viewModel: SpeechRecognitionViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var showingLanguageSelection = false

    // Direct observation of translation service for reactive UI updates
    @ObservedObject private var translationService: HybridTranslationService

    init(viewModel: SpeechRecognitionViewModel) {
        self.viewModel = viewModel
        self._translationService = ObservedObject(wrappedValue: viewModel.translationService)
    }

    var body: some View {
        NavigationView {
            ZStack {
                // Clean Alabaster background with French Lilac gradient
                DesignSystem.brandColors.adaptiveBackgroundGradient
                    .ignoresSafeArea()
                
                ScrollView {
                    LazyVStack(spacing: DesignSystem.spacing.settingsSectionSpacing) {
                        translationSection
                        aboutSection
                    }
                    .padding(.horizontal, DesignSystem.spacing.screenPadding)
                    .padding(.top, DesignSystem.spacing.small)
                }
            }
//            .navigationTitle("Settings")
//            .navigationBarTitleDisplayMode(.inline)
            .toolbarBackground(.clear, for: .navigationBar)
            .toolbarBackground(.hidden, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    ToolbarMenuButton()
                }
            }
        }
        .sheet(isPresented: $showingLanguageSelection) {
            LanguageSelectionSheet(translationService: translationService)
        }

    }
    
    // MARK: - Modern Section Views

    private var translationSection: some View {
        VStack(alignment: .leading, spacing: DesignSystem.spacing.medium) {
            sectionHeader("Translation")

            VStack(spacing: DesignSystem.spacing.small) {
                // Auto-translation toggle using BrandToggle
                BrandToggle(
                    "Auto-translate",
                    isOn: $translationService.isTranslationEnabled,
                    description: "Automatically translate transcriptions"
                )
                .padding(.vertical, DesignSystem.spacing.small)

                // Default target language selection
                if translationService.isTranslationEnabled {
                    VStack(spacing: DesignSystem.spacing.small) {
                        // French Lilac section divider
                        Divider()
                            .background(DesignSystem.brandColors.frenchLilac)

                        HStack {
                            Image(systemName: "globe")
                                .font(DesignSystem.typography.body)
                                .foregroundColor(DesignSystem.brandColors.orchid)
                                .frame(width: 20)

                            VStack(alignment: .leading, spacing: 2) {
                                Text("Default Language")
                                    .font(DesignSystem.typography.body)
                                    .foregroundColor(DesignSystem.colors.textPrimary)

                                Text("Target language for translations")
                                    .font(DesignSystem.typography.caption)
                                    .foregroundColor(DesignSystem.colors.textSecondary)
                            }

                            Spacer()

                            // Compact language display
                            HStack(spacing: 6) {
                                Text(translationService.selectedTargetLanguage.flag)
                                    .font(.system(size: 16))

                                Text(translationService.selectedTargetLanguage.displayName)
                                    .font(DesignSystem.typography.caption)
                                    .foregroundColor(DesignSystem.colors.textSecondary)

                                Image(systemName: "chevron.right")
                                    .font(DesignSystem.typography.caption2)
                                    .foregroundColor(DesignSystem.brandColors.orchid)
                            }
                        }
                        .padding(.vertical, DesignSystem.spacing.small)
                        .contentShape(Rectangle())
                        .onTapGesture {
                            HapticPattern.light.trigger()
                            showingLanguageSelection = true
                        }
                        .accessibilityElement(children: .combine)
                        .accessibilityLabel("Default Language")
                        .accessibilityValue(translationService.selectedTargetLanguage.displayName)
                        .accessibilityHint("Double tap to change target language for translations")
                        .accessibilityAddTraits(.isButton)
                    }
                    .transition(.opacity.combined(with: .move(edge: .top)))
                }
            }
            .padding(DesignSystem.spacing.medium)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(DesignSystem.brandColors.alabaster)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(DesignSystem.brandColors.frenchLilac, lineWidth: 1)
                    )
                    .shadow(
                        color: DesignSystem.brandColors.persianPurple.opacity(0.1),
                        radius: 4,
                        x: 0,
                        y: 2
                    )
            )
        }
        .animation(.easeInOut(duration: 0.3), value: translationService.isTranslationEnabled)
    }

    private var aboutSection: some View {
        VStack(alignment: .leading, spacing: DesignSystem.spacing.medium) {
            sectionHeader("About")

            VStack(spacing: DesignSystem.spacing.small) {
                aboutRow("App Version", value: "1.0.0", icon: "app.badge")
//                aboutRow("Build", value: "1", icon: "hammer")

                #if DEBUG
                // Debug Translation Tests (only in debug builds)
                NavigationLink(destination: TranslationTestView()) {
                    HStack(spacing: DesignSystem.spacing.medium) {
                        Image(systemName: "testtube.2")
                            .font(DesignSystem.typography.body)
                            .foregroundColor(DesignSystem.brandColors.orchid)
                            .frame(width: 20)

                        VStack(alignment: .leading, spacing: 2) {
                            Text("Translation Tests")
                                .font(DesignSystem.typography.body)
                                .foregroundColor(DesignSystem.colors.textPrimary)

                            Text("Debug translation services")
                                .font(DesignSystem.typography.caption)
                                .foregroundColor(DesignSystem.colors.textSecondary)
                        }

                        Spacer()

                        Image(systemName: "chevron.right")
                            .font(DesignSystem.typography.caption)
                            .foregroundColor(DesignSystem.brandColors.orchid)
                    }
                    .padding(.vertical, DesignSystem.spacing.small)
                }
                .buttonStyle(PlainButtonStyle())

                // API Key Setup (only in debug builds)
                NavigationLink(destination: APIKeySetupView()) {
                    HStack(spacing: DesignSystem.spacing.medium) {
                        Image(systemName: "key.fill")
                            .font(DesignSystem.typography.body)
                            .foregroundColor(DesignSystem.brandColors.orchid)
                            .frame(width: 20)

                        VStack(alignment: .leading, spacing: 2) {
                            Text("API Key Setup")
                                .font(DesignSystem.typography.body)
                                .foregroundColor(DesignSystem.colors.textPrimary)

                            Text("Secure Google Cloud API key")
                                .font(DesignSystem.typography.caption)
                                .foregroundColor(DesignSystem.colors.textSecondary)
                        }

                        Spacer()

                        Image(systemName: "chevron.right")
                            .font(DesignSystem.typography.caption)
                            .foregroundColor(DesignSystem.brandColors.orchid)
                    }
                    .padding(.vertical, DesignSystem.spacing.small)
                }
                .buttonStyle(PlainButtonStyle())
                #endif

                // Source Code Link
//                Button(action: {
//                    HapticPattern.light.trigger()
//                    if let url = URL(string: "https://github.com/your-repo/RockerSTT") {
//                        UIApplication.shared.open(url)
//                    }
//                }) {
//                    HStack(spacing: DesignSystem.spacing.medium) {
//                        Image(systemName: "link")
//                            .font(DesignSystem.typography.body)
//                            .foregroundColor(DesignSystem.brandColors.orchid)
//                            .frame(width: 20)
//
//                        Text("Source Code")
//                            .font(DesignSystem.typography.body)
//                            .foregroundColor(DesignSystem.colors.textPrimary)
//
//                        Spacer()
//
//                        Image(systemName: "arrow.up.right")
//                            .font(DesignSystem.typography.caption)
//                            .foregroundColor(DesignSystem.brandColors.orchid)
//                    }
//                    .padding(.vertical, DesignSystem.spacing.small)
//                }
            }
            .padding(DesignSystem.spacing.medium)
            .brandCardStyle()
        }
    }
    
    private func aboutRow(_ title: String, value: String, icon: String) -> some View {
        HStack(spacing: DesignSystem.spacing.medium) {
            Image(systemName: icon)
                .font(DesignSystem.typography.body)
                .foregroundColor(DesignSystem.brandColors.orchid)
                .frame(width: 20)
            
            Text(title)
                .font(DesignSystem.typography.body)
                .foregroundColor(DesignSystem.colors.textPrimary)
            
            Spacer()
            
            Text(value)
                .font(DesignSystem.typography.body)
                .foregroundColor(DesignSystem.colors.textSecondary)
        }
        .padding(.vertical, DesignSystem.spacing.small)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(title): \(value)")
        .accessibilityAddTraits(.isStaticText)
    }
    
    // MARK: - Helper Functions
    
    private func sectionHeader(_ title: String) -> some View {
        Text(title)
            .font(DesignSystem.typography.title3)
            .fontWeight(.medium)
            .foregroundColor(DesignSystem.brandColors.persianPurple)
            .padding(.horizontal, DesignSystem.spacing.micro)
    }
}

// MARK: - Preview
#Preview {
    SettingsView(viewModel: SpeechRecognitionViewModel())
}
