//
//  NavigationHelpers.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025-07-21.
//

import SwiftUI
import Foundation

/// Helper functions for navigation throughout the app
struct NavigationHelpers {
    
    /// Navigate to a specific session from anywhere in the app
    static func navigateToSession(_ session: HistorySession) {
        NotificationCenter.default.post(
            name: .navigateToSession,
            object: session
        )
    }
    
    /// Navigate to history search from anywhere in the app
    static func navigateToHistorySearch() {
        NotificationCenter.default.post(
            name: .navigateToHistorySearch,
            object: nil
        )
    }
    
    /// Navigate to settings with optional section
    static func navigateToSettings(section: SettingsSection? = nil) {
        NotificationCenter.default.post(
            name: .navigateToSettings,
            object: section
        )
    }
    
    /// Share a session using the system share sheet
    static func shareSession(_ session: HistorySession, from view: UIView) {
        guard let text = session.fullTranscriptionText else { return }
        
        let activityViewController = UIActivityViewController(
            activityItems: [text],
            applicationActivities: nil
        )
        
        // For iPad
        if let popover = activityViewController.popoverPresentationController {
            popover.sourceView = view
            popover.sourceRect = view.bounds
        }
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first,
           let rootViewController = window.rootViewController {
            rootViewController.present(activityViewController, animated: true)
        }
    }
    
    /// Open URL in external browser
    static func openURL(_ url: URL) {
        UIApplication.shared.open(url)
    }
    
    /// Generate shareable link for a session
    @MainActor
    static func generateShareableLink(for session: HistorySession) -> URL? {
        return NavigationCoordinator.sessionDeepLink(sessionId: session.id ?? UUID())
    }
}

// MARK: - SwiftUI Environment Integration

/// Environment key for navigation coordinator
struct NavigationCoordinatorKey: EnvironmentKey {
    static let defaultValue: NavigationCoordinator? = nil
}

extension EnvironmentValues {
    var navigationCoordinator: NavigationCoordinator? {
        get { self[NavigationCoordinatorKey.self] }
        set { self[NavigationCoordinatorKey.self] = newValue }
    }
}

// MARK: - View Extensions

extension View {
    
    /// Add navigation coordinator to environment
    func withNavigationCoordinator(_ coordinator: NavigationCoordinator) -> some View {
        self.environment(\.navigationCoordinator, coordinator)
    }
    
    /// Navigate to session using environment coordinator
    func navigateToSession(_ session: HistorySession) -> some View {
        self.onTapGesture {
            NavigationHelpers.navigateToSession(session)
        }
    }
    
    /// Add quick navigation toolbar
    func withQuickNavigation() -> some View {
        self.toolbar {
            ToolbarItemGroup(placement: .navigationBarTrailing) {
                QuickNavigationMenu()
            }
        }
    }
}

// MARK: - Quick Navigation Menu

struct QuickNavigationMenu: View {
    @Environment(\.navigationCoordinator) private var coordinator
    
    var body: some View {
        Menu {
            Button(action: {
                NavigationHelpers.navigateToHistorySearch()
            }) {
                Label("Search History", systemImage: "magnifyingglass")
            }
            
            Button(action: {
                NavigationHelpers.navigateToSettings()
            }) {
                Label("Settings", systemImage: "gear")
            }
            
            Divider()
            
            Button(action: {
                NavigationHelpers.navigateToSettings(section: .developer)
            }) {
                Label("Developer Tools", systemImage: "hammer")
            }
        } label: {
            Image(systemName: "ellipsis.circle")
        }
    }
}

// MARK: - Navigation State Manager

/// Manages navigation state across the app
class NavigationStateManager: ObservableObject {
    
    @Published var isNavigating = false
    @Published var navigationHistory: [NavigationDestination] = []
    @Published var canGoBack = false
    @Published var canGoForward = false
    
    private var currentIndex = -1
    
    /// Add a navigation destination to history
    func addToHistory(_ destination: NavigationDestination) {
        // Remove any forward history if we're not at the end
        if currentIndex < navigationHistory.count - 1 {
            navigationHistory.removeSubrange((currentIndex + 1)...)
        }
        
        navigationHistory.append(destination)
        currentIndex = navigationHistory.count - 1
        updateNavigationState()
    }
    
    /// Go back in navigation history
    func goBack() -> NavigationDestination? {
        guard canGoBack else { return nil }
        
        currentIndex -= 1
        updateNavigationState()
        return navigationHistory[currentIndex]
    }
    
    /// Go forward in navigation history
    func goForward() -> NavigationDestination? {
        guard canGoForward else { return nil }
        
        currentIndex += 1
        updateNavigationState()
        return navigationHistory[currentIndex]
    }
    
    /// Clear navigation history
    func clearHistory() {
        navigationHistory.removeAll()
        currentIndex = -1
        updateNavigationState()
    }
    
    private func updateNavigationState() {
        canGoBack = currentIndex > 0
        canGoForward = currentIndex < navigationHistory.count - 1
    }
}

// MARK: - Navigation Analytics

/// Track navigation patterns for analytics
struct NavigationAnalytics {
    
    /// Track navigation event
    static func trackNavigation(from source: String, to destination: String) {
        let event = [
            "source": source,
            "destination": destination,
            "timestamp": ISO8601DateFormatter().string(from: Date())
        ]
        
        print("📊 Navigation: \(event)")
        // In a real app, you would send this to your analytics service
    }
    
    /// Track tab switch
    static func trackTabSwitch(from oldTab: AppTab, to newTab: AppTab) {
        trackNavigation(from: "tab_\(oldTab.title.lowercased())", to: "tab_\(newTab.title.lowercased())")
    }
    
    /// Track deep link usage
    static func trackDeepLink(_ url: URL) {
        let event = [
            "url": url.absoluteString,
            "timestamp": ISO8601DateFormatter().string(from: Date())
        ]
        
        print("🔗 Deep Link: \(event)")
    }
}

// MARK: - Navigation Shortcuts

/// Keyboard shortcuts for navigation
struct NavigationShortcuts: View {
    @Environment(\.navigationCoordinator) private var coordinator

    var body: some View {
        VStack {
            Button("Record Tab") {
                coordinator?.navigateToTab(.record)
            }
            .keyboardShortcut("1", modifiers: .command)
            .hidden()

            Button("History Tab") {
                coordinator?.navigateToTab(.history)
            }
            .keyboardShortcut("2", modifiers: .command)
            .hidden()

            Button("Settings Tab") {
                coordinator?.navigateToTab(.settings)
            }
            .keyboardShortcut("3", modifiers: .command)
            .hidden()

            Button("Search") {
                NavigationHelpers.navigateToHistorySearch()
            }
            .keyboardShortcut("f", modifiers: .command)
            .hidden()

            Button("Settings") {
                NavigationHelpers.navigateToSettings()
            }
            .keyboardShortcut(",", modifiers: .command)
            .hidden()
        }
        .frame(width: 0, height: 0)
        .opacity(0)
    }
}

// MARK: - URL Scheme Handler

/// Handle custom URL schemes for the app
struct URLSchemeHandler {
    
    /// Register URL scheme with the system
    static func registerURLScheme() {
        // This would be configured in Info.plist
        // URL scheme: rockerstt://
    }
    
    /// Parse and handle incoming URLs
    @MainActor
    static func handleURL(_ url: URL, coordinator: NavigationCoordinator) {
        NavigationAnalytics.trackDeepLink(url)
        coordinator.handleDeepLink(url)
    }
    
    /// Generate URL for sharing
    static func generateShareURL(for session: HistorySession) -> URL? {
        guard let sessionId = session.id else { return nil }
        return URL(string: "rockerstt://history/session/\(sessionId.uuidString)")
    }
}
